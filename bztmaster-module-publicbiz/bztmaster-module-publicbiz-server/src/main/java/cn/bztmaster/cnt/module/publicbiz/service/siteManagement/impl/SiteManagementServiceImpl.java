package cn.bztmaster.cnt.module.publicbiz.service.siteManagement.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.siteManagement.SiteManagementConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteManagementDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.siteManagement.SiteManagementMapper;
import cn.bztmaster.cnt.module.publicbiz.enums.SiteStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.siteManagement.SiteManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 场地管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SiteManagementServiceImpl implements SiteManagementService {

    @Resource
    private SiteManagementMapper siteManagementMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSiteManagement(SiteManagementSaveReqVO createReqVO) {
        // 校验场地名称唯一性
        validateSiteManagementNameUnique(null, createReqVO.getName());

        // 插入
        SiteManagementDO siteManagement = SiteManagementConvert.INSTANCE.convertFromVO(createReqVO);
        siteManagementMapper.insert(siteManagement);
        
        // 返回
        return siteManagement.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSiteManagement(SiteManagementSaveReqVO updateReqVO) {
        // 校验存在
        validateSiteManagementExists(updateReqVO.getId());
        // 校验场地名称唯一性
        validateSiteManagementNameUnique(updateReqVO.getId(), updateReqVO.getName());

        // 更新
        SiteManagementDO updateObj = SiteManagementConvert.INSTANCE.convertFromVO(updateReqVO);
        siteManagementMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSiteManagement(Long id) {
        // 校验存在
        validateSiteManagementExists(id);
        
        // TODO: 校验是否有关联的预约记录，如果有则不允许删除
        
        // 删除
        siteManagementMapper.deleteById(id);
    }

    @Override
    public SiteManagementDO getSiteManagement(Long id) {
        return siteManagementMapper.selectById(id);
    }

    @Override
    public PageResult<SiteManagementDO> getSiteManagementPage(SiteManagementPageReqVO pageReqVO) {
        return siteManagementMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SiteManagementDO> getSiteManagementList(SiteManagementListReqVO listReqVO) {
        return siteManagementMapper.selectList(listReqVO);
    }

    @Override
    public SiteManagementStatisticsRespVO getSiteManagementStatistics(String campus, String campusName) {
        SiteManagementStatisticsRespVO statistics = new SiteManagementStatisticsRespVO();
        
        // 统计总数
        Long totalCount = siteManagementMapper.countByStatus(null, campus, campusName);
        statistics.setTotalCount(totalCount.intValue());
        
        // 统计各状态数量
        Long availableCount = siteManagementMapper.countByStatus(SiteStatusEnum.AVAILABLE.getStatus(), campus, campusName);
        statistics.setAvailableCount(availableCount.intValue());
        
        Long reservedCount = siteManagementMapper.countByStatus(SiteStatusEnum.RESERVED.getStatus(), campus, campusName);
        statistics.setReservedCount(reservedCount.intValue());
        
        Long maintenanceCount = siteManagementMapper.countByStatus(SiteStatusEnum.MAINTENANCE.getStatus(), campus, campusName);
        statistics.setMaintenanceCount(maintenanceCount.intValue());
        
        Long disabledCount = siteManagementMapper.countByStatus(SiteStatusEnum.DISABLED.getStatus(), campus, campusName);
        statistics.setDisabledCount(disabledCount.intValue());
        
        return statistics;
    }

    @Override
    public SiteManagementDO validateSiteManagementExists(Long id) {
        if (id == null) {
            return null;
        }
        SiteManagementDO siteManagement = siteManagementMapper.selectById(id);
        if (siteManagement == null) {
            throw exception(SITE_MANAGEMENT_NOT_EXISTS);
        }
        return siteManagement;
    }

    @Override
    public void validateSiteManagementNameUnique(Long id, String name) {
        SiteManagementDO siteManagement = siteManagementMapper.selectByName(name);
        if (siteManagement == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的场地
        if (id == null) {
            throw exception(SITE_MANAGEMENT_NAME_DUPLICATE);
        }
        if (!siteManagement.getId().equals(id)) {
            throw exception(SITE_MANAGEMENT_NAME_DUPLICATE);
        }
    }

    @Override
    public SiteManagementDO validateSiteManagementBookable(Long id) {
        SiteManagementDO siteManagement = validateSiteManagementExists(id);
        if (!SiteStatusEnum.isBookable(siteManagement.getStatus())) {
            throw exception(SITE_MANAGEMENT_NOT_BOOKABLE);
        }
        return siteManagement;
    }

}
