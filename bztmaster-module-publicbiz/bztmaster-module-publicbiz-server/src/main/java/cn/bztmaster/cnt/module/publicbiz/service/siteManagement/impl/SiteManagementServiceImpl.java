package cn.bztmaster.cnt.module.publicbiz.service.siteManagement.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.siteManagement.SiteManagementConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteManagementDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.siteManagement.SiteManagementMapper;
import cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants;
import cn.bztmaster.cnt.module.publicbiz.enums.SiteStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.siteManagement.SiteManagementService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 场地管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SiteManagementServiceImpl implements SiteManagementService {

    @Resource
    private SiteManagementMapper siteManagementMapper;

    /**
     * 新增场地
     *
     * 操作日志记录：
     * - 记录新增操作，包含场地名称和校区名称
     * - 使用 @LogRecord 注解自动记录操作日志
     * - 通过 LogRecordContext.putVariable 设置日志上下文变量
     *
     * @param createReqVO 新增请求参数
     * @return 场地ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.SITE_MANAGEMENT_TYPE,
               subType = LogRecordConstants.SITE_MANAGEMENT_CREATE_SUB_TYPE,
               bizNo = "{{#siteManagement.id}}",
               success = LogRecordConstants.SITE_MANAGEMENT_CREATE_SUCCESS)
    public Long createSiteManagement(SiteManagementSaveReqVO createReqVO) {
        // 校验场地名称唯一性
        validateSiteManagementNameUnique(null, createReqVO.getName());

        // 插入
        SiteManagementDO siteManagement = SiteManagementConvert.INSTANCE.convertFromVO(createReqVO);
        siteManagementMapper.insert(siteManagement);

        // 记录操作日志上下文 - 用于日志模板中的变量替换
        LogRecordContext.putVariable("siteManagement", siteManagement);

        // 返回
        return siteManagement.getId();
    }

    /**
     * 更新场地
     *
     * 操作日志记录：
     * - 记录更新操作，包含场地名称
     * - 使用 _DIFF 函数记录字段级别的变更
     * - 通过 LogRecordContext.putVariable 设置新旧对象用于对比
     *
     * @param updateReqVO 更新请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.SITE_MANAGEMENT_TYPE,
               subType = LogRecordConstants.SITE_MANAGEMENT_UPDATE_SUB_TYPE,
               bizNo = "{{#updateReqVO.id}}",
               success = LogRecordConstants.SITE_MANAGEMENT_UPDATE_SUCCESS)
    public void updateSiteManagement(SiteManagementSaveReqVO updateReqVO) {
        // 获取更新前的场地信息 - 用于操作日志的字段对比
        SiteManagementDO oldSiteManagement = validateSiteManagementExists(updateReqVO.getId());

        // 校验场地名称唯一性
        validateSiteManagementNameUnique(updateReqVO.getId(), updateReqVO.getName());

        // 更新
        SiteManagementDO updateObj = SiteManagementConvert.INSTANCE.convertFromVO(updateReqVO);
        siteManagementMapper.updateById(updateObj);

        // 记录操作日志上下文 - 用于字段对比和日志模板中的变量替换
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(oldSiteManagement, SiteManagementSaveReqVO.class));
        LogRecordContext.putVariable("siteManagement", oldSiteManagement);
    }

    /**
     * 删除场地
     *
     * 操作日志记录：
     * - 记录删除操作，包含场地名称和校区名称
     * - 使用 @LogRecord 注解自动记录操作日志
     * - 通过 LogRecordContext.putVariable 设置日志上下文变量
     *
     * @param id 场地ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.SITE_MANAGEMENT_TYPE,
               subType = LogRecordConstants.SITE_MANAGEMENT_DELETE_SUB_TYPE,
               bizNo = "{{#siteManagement.id}}",
               success = LogRecordConstants.SITE_MANAGEMENT_DELETE_SUCCESS)
    public void deleteSiteManagement(Long id) {
        // 校验存在并获取场地信息 - 用于操作日志记录
        SiteManagementDO siteManagement = validateSiteManagementExists(id);

        // TODO: 校验是否有关联的预约记录，如果有则不允许删除

        // 删除
        siteManagementMapper.deleteById(id);

        // 记录操作日志上下文 - 用于日志模板中的变量替换
        LogRecordContext.putVariable("siteManagement", siteManagement);
    }

    @Override
    public SiteManagementDO getSiteManagement(Long id) {
        return siteManagementMapper.selectById(id);
    }

    @Override
    public PageResult<SiteManagementDO> getSiteManagementPage(SiteManagementPageReqVO pageReqVO) {
        return siteManagementMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SiteManagementDO> getSiteManagementList(SiteManagementListReqVO listReqVO) {
        return siteManagementMapper.selectList(listReqVO);
    }

    @Override
    public SiteManagementStatisticsRespVO getSiteManagementStatistics(String campus, String campusName) {
        SiteManagementStatisticsRespVO statistics = new SiteManagementStatisticsRespVO();
        
        // 统计总数
        Long totalCount = siteManagementMapper.countByStatus(null, campus, campusName);
        statistics.setTotalCount(totalCount.intValue());
        
        // 统计各状态数量
        Long availableCount = siteManagementMapper.countByStatus(SiteStatusEnum.AVAILABLE.getStatus(), campus, campusName);
        statistics.setAvailableCount(availableCount.intValue());
        
        Long reservedCount = siteManagementMapper.countByStatus(SiteStatusEnum.RESERVED.getStatus(), campus, campusName);
        statistics.setReservedCount(reservedCount.intValue());
        
        Long maintenanceCount = siteManagementMapper.countByStatus(SiteStatusEnum.MAINTENANCE.getStatus(), campus, campusName);
        statistics.setMaintenanceCount(maintenanceCount.intValue());
        
        Long disabledCount = siteManagementMapper.countByStatus(SiteStatusEnum.DISABLED.getStatus(), campus, campusName);
        statistics.setDisabledCount(disabledCount.intValue());
        
        return statistics;
    }

    @Override
    public SiteManagementDO validateSiteManagementExists(Long id) {
        if (id == null) {
            return null;
        }
        SiteManagementDO siteManagement = siteManagementMapper.selectById(id);
        if (siteManagement == null) {
            throw exception(SITE_MANAGEMENT_NOT_EXISTS);
        }
        return siteManagement;
    }

    @Override
    public void validateSiteManagementNameUnique(Long id, String name) {
        SiteManagementDO siteManagement = siteManagementMapper.selectByName(name);
        if (siteManagement == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的场地
        if (id == null) {
            throw exception(SITE_MANAGEMENT_NAME_DUPLICATE);
        }
        if (!siteManagement.getId().equals(id)) {
            throw exception(SITE_MANAGEMENT_NAME_DUPLICATE);
        }
    }

    @Override
    public SiteManagementDO validateSiteManagementBookable(Long id) {
        SiteManagementDO siteManagement = validateSiteManagementExists(id);
        if (!SiteStatusEnum.isBookable(siteManagement.getStatus())) {
            throw exception(SITE_MANAGEMENT_NOT_BOOKABLE);
        }
        return siteManagement;
    }

}
