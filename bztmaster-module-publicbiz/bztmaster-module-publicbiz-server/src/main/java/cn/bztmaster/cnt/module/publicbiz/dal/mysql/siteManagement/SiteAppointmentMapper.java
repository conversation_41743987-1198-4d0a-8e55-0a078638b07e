package cn.bztmaster.cnt.module.publicbiz.dal.mysql.siteManagement;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteAppointmentBySiteAndDateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteAppointmentListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteAppointmentPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteAppointmentDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 场地预约 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SiteAppointmentMapper extends BaseMapperX<SiteAppointmentDO> {

    /**
     * 分页查询预约列表
     *
     * @param reqVO 分页查询条件
     * @return 分页结果
     */
    default PageResult<SiteAppointmentDO> selectPage(SiteAppointmentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SiteAppointmentDO>()
                .eqIfPresent(SiteAppointmentDO::getSiteId, reqVO.getSiteId())
                .eqIfPresent(SiteAppointmentDO::getActivityType, reqVO.getActivityType())
                .eqIfPresent(SiteAppointmentDO::getStatus, reqVO.getStatus())
                .geIfPresent(SiteAppointmentDO::getStartDate, reqVO.getStartDate())
                .leIfPresent(SiteAppointmentDO::getEndDate, reqVO.getEndDate())
                .likeIfPresent(SiteAppointmentDO::getContactName, reqVO.getContactName())
                .orderByDesc(SiteAppointmentDO::getId));
    }

    /**
     * 查询预约列表
     *
     * @param reqVO 查询条件
     * @return 预约列表
     */
    default List<SiteAppointmentDO> selectList(SiteAppointmentListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SiteAppointmentDO>()
                .eqIfPresent(SiteAppointmentDO::getSiteId, reqVO.getSiteId())
                .eqIfPresent(SiteAppointmentDO::getActivityType, reqVO.getActivityType())
                .eqIfPresent(SiteAppointmentDO::getStatus, reqVO.getStatus())
                .geIfPresent(SiteAppointmentDO::getStartDate, reqVO.getStartDate())
                .leIfPresent(SiteAppointmentDO::getEndDate, reqVO.getEndDate())
                .likeIfPresent(SiteAppointmentDO::getContactName, reqVO.getContactName())
                .orderByDesc(SiteAppointmentDO::getId));
    }

    /**
     * 根据场地和日期查询预约列表
     *
     * @param reqVO 查询条件
     * @return 预约列表
     */
    default List<SiteAppointmentDO> selectListBySiteAndDate(SiteAppointmentBySiteAndDateReqVO reqVO) {
        LambdaQueryWrapperX<SiteAppointmentDO> wrapper = new LambdaQueryWrapperX<SiteAppointmentDO>()
                .eq(SiteAppointmentDO::getSiteId, reqVO.getSiteId());

        // 单日期查询
        if (reqVO.getDate() != null) {
            wrapper.le(SiteAppointmentDO::getStartDate, reqVO.getDate())
                   .ge(SiteAppointmentDO::getEndDate, reqVO.getDate());
        }
        // 日期区间查询
        else if (reqVO.getStartDate() != null && reqVO.getEndDate() != null) {
            wrapper.le(SiteAppointmentDO::getStartDate, reqVO.getEndDate())
                   .ge(SiteAppointmentDO::getEndDate, reqVO.getStartDate());
        }

        return selectList(wrapper.orderByAsc(SiteAppointmentDO::getStartDate)
                                .orderByAsc(SiteAppointmentDO::getStartTime));
    }

    /**
     * 检查时间冲突
     *
     * @param siteId 场地ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param excludeId 排除的预约ID（用于更新时排除自己）
     * @return 冲突的预约列表
     */
    default List<SiteAppointmentDO> selectConflictAppointments(Long siteId, LocalDate startDate, LocalDate endDate,
                                                               LocalTime startTime, LocalTime endTime, Long excludeId) {
        return selectList(new LambdaQueryWrapperX<SiteAppointmentDO>()
                .eq(SiteAppointmentDO::getSiteId, siteId)
                .neIfPresent(SiteAppointmentDO::getId, excludeId)
                .in(SiteAppointmentDO::getStatus, "已确认", "待确认") // 只检查有效状态的预约
                // 日期重叠检查
                .le(SiteAppointmentDO::getStartDate, endDate)
                .ge(SiteAppointmentDO::getEndDate, startDate)
                // 时间重叠检查
                .and(w -> w.lt(SiteAppointmentDO::getStartTime, endTime)
                          .gt(SiteAppointmentDO::getEndTime, startTime)));
    }

    /**
     * 根据场地ID查询预约列表
     *
     * @param siteId 场地ID
     * @return 预约列表
     */
    default List<SiteAppointmentDO> selectListBySiteId(Long siteId) {
        return selectList(SiteAppointmentDO::getSiteId, siteId);
    }

    /**
     * 根据状态查询预约列表
     *
     * @param status 状态
     * @return 预约列表
     */
    default List<SiteAppointmentDO> selectListByStatus(String status) {
        return selectList(SiteAppointmentDO::getStatus, status);
    }

}
