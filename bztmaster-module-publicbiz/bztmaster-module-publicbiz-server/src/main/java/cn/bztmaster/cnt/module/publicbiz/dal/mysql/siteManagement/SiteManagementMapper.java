package cn.bztmaster.cnt.module.publicbiz.dal.mysql.siteManagement;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteManagementListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteManagementPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteManagementDO;
import cn.hutool.core.util.StrUtil;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 场地管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SiteManagementMapper extends BaseMapperX<SiteManagementDO> {

    /**
     * 分页查询场地列表
     *
     * @param reqVO 分页查询条件
     * @return 分页结果
     */
    default PageResult<SiteManagementDO> selectPage(SiteManagementPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SiteManagementDO>()
                .likeIfPresent(SiteManagementDO::getCampus, reqVO.getCampus())
                .likeIfPresent(SiteManagementDO::getCampusName, reqVO.getCampusName())
                .eqIfPresent(SiteManagementDO::getType, reqVO.getType())
                .eqIfPresent(SiteManagementDO::getStatus, reqVO.getStatus())
                .and(StrUtil.isNotBlank(reqVO.getKeyword()), w -> w
                        .like(SiteManagementDO::getName, reqVO.getKeyword()))
                .and(StrUtil.isNotBlank(reqVO.getCapacity()), w -> {
                    // 解析容量范围，如 "50-100"
                    String[] range = reqVO.getCapacity().split("-");
                    if (range.length == 2) {
                        try {
                            int min = Integer.parseInt(range[0].trim());
                            int max = Integer.parseInt(range[1].trim());
                            w.ge(SiteManagementDO::getSeatTotal, min)
                             .le(SiteManagementDO::getSeatTotal, max);
                        } catch (NumberFormatException ignored) {
                            // 忽略格式错误
                        }
                    }
                })
                .orderByDesc(SiteManagementDO::getId));
    }

    /**
     * 查询场地列表
     *
     * @param reqVO 查询条件
     * @return 场地列表
     */
    default List<SiteManagementDO> selectList(SiteManagementListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SiteManagementDO>()
                .likeIfPresent(SiteManagementDO::getCampus, reqVO.getCampus())
                .likeIfPresent(SiteManagementDO::getCampusName, reqVO.getCampusName())
                .eqIfPresent(SiteManagementDO::getType, reqVO.getType())
                .eqIfPresent(SiteManagementDO::getStatus, reqVO.getStatus())
                .likeIfPresent(SiteManagementDO::getName, reqVO.getName())
                .orderByDesc(SiteManagementDO::getId));
    }

    /**
     * 根据名称查询场地
     *
     * @param name 场地名称
     * @return 场地信息
     */
    default SiteManagementDO selectByName(String name) {
        return selectOne(SiteManagementDO::getName, name);
    }

    /**
     * 根据校区查询场地列表
     *
     * @param campus 校区
     * @return 场地列表
     */
    default List<SiteManagementDO> selectListByCampus(String campus) {
        return selectList(SiteManagementDO::getCampus, campus);
    }

    /**
     * 根据状态查询场地列表
     *
     * @param status 状态
     * @return 场地列表
     */
    default List<SiteManagementDO> selectListByStatus(String status) {
        return selectList(SiteManagementDO::getStatus, status);
    }

    /**
     * 统计各状态场地数量
     *
     * @param campus 校区筛选（可选）
     * @param campusName 校区名称筛选（可选）
     * @return 统计结果
     */
    default Long countByStatus(String status, String campus, String campusName) {
        return selectCount(new LambdaQueryWrapperX<SiteManagementDO>()
                .eqIfPresent(SiteManagementDO::getStatus, status)
                .likeIfPresent(SiteManagementDO::getCampus, campus)
                .likeIfPresent(SiteManagementDO::getCampusName, campusName));
    }

}
