package cn.bztmaster.cnt.module.publicbiz.enums;

import cn.bztmaster.cnt.framework.common.exception.ErrorCode;

/**
 * 业务错误码枚举类
 *
 * publicbiz 系统，使用 1-001-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 线索模块 1-001-001-000 ==========
    ErrorCode LEAD_NOT_EXISTS = new ErrorCode(1_001_001_000, "线索不存在");
    ErrorCode LEAD_ID_EXISTS = new ErrorCode(1_001_001_001, "线索ID已存在");
    ErrorCode LEAD_FOLLOW_UP_LOG_NOT_EXISTS = new ErrorCode(1_001_001_002, "线索跟进记录不存在");
    ErrorCode LEAD_CUSTOMER_PHONE_EXISTS = new ErrorCode(1_001_001_003, "客户联系电话已存在");

    // ========== 数字资产课程模块 1-001-002-000 ==========
    ErrorCode DIGITAL_ASSET_COURSE_NOT_EXISTS = new ErrorCode(1_001_002_000, "课程不存在");
    ErrorCode DIGITAL_ASSET_COURSE_NAME_DUPLICATE = new ErrorCode(1_001_002_001, "课程名称已存在");
    ErrorCode DIGITAL_ASSET_COURSE_STATUS_INVALID = new ErrorCode(1_001_002_002, "课程状态无效");
    ErrorCode DIGITAL_ASSET_COURSE_OFFLINE_NOT_SUPPORT_CHAPTER = new ErrorCode(1_001_002_003, "线下课程不支持章节课时管理");

    // ========== 课程章节模块 1-001-003-000 ==========
    ErrorCode COURSE_CHAPTER_NOT_EXISTS = new ErrorCode(1_001_003_000, "章节不存在");
    ErrorCode COURSE_CHAPTER_TITLE_DUPLICATE = new ErrorCode(1_001_003_001, "章节标题已存在");

    // ========== 课程课时模块 1-001-004-000 ==========
    ErrorCode COURSE_LESSON_NOT_EXISTS = new ErrorCode(1_001_004_000, "课时不存在");
    ErrorCode COURSE_LESSON_TITLE_DUPLICATE = new ErrorCode(1_001_004_001, "课时标题已存在");

    // ========== 课程附件模块 1-001-005-000 ==========
    ErrorCode COURSE_ATTACHMENT_NOT_EXISTS = new ErrorCode(1_001_005_000, "附件不存在");
    ErrorCode COURSE_ATTACHMENT_NAME_DUPLICATE = new ErrorCode(1_001_005_001, "附件名称已存在");

    // ========== 服务分类相关 1-001-006-000 ==========
    ErrorCode CATEGORY_NOT_EXISTS = new ErrorCode(1_001_006_000, "服务分类不存在");
    ErrorCode CATEGORY_EXISTS_CHILDREN = new ErrorCode(1_001_006_001, "存在子服务分类，无法删除");
    ErrorCode CATEGORY_NOT_ENABLE = new ErrorCode(1_001_006_002, "服务分类未启用");
    ErrorCode CATEGORY_LEVEL_TOO_DEEP = new ErrorCode(1_001_006_003, "服务分类层级过深");
    ErrorCode CATEGORY_NAME_EXISTS = new ErrorCode(1_001_006_004, "服务分类名称已存在");
    ErrorCode CATEGORY_PARENT_NOT_EXISTS = new ErrorCode(1_001_006_005, "父服务分类不存在");
    ErrorCode CATEGORY_STATUS_ERROR = new ErrorCode(1_001_006_006, "服务分类状态错误");

    // ========== 考题管理模块 1-009-006-000 ==========
    ErrorCode QUESTION_NOT_EXISTS = new ErrorCode(1_009_006_000, "考题不存在");
    ErrorCode QUESTION_DELETED = new ErrorCode(1_009_006_001, "考题已被删除");
    ErrorCode QUESTION_TYPE_NOT_SUPPORT = new ErrorCode(1_009_006_002, "题型不支持");
    ErrorCode QUESTION_IMPORT_FILE_FORMAT_ERROR = new ErrorCode(1_009_006_003, "文件格式不正确");
    ErrorCode QUESTION_IMPORT_FILE_SIZE_EXCEED = new ErrorCode(1_009_006_004, "文件大小超限");
    ErrorCode QUESTION_IMPORT_DATA_FORMAT_ERROR = new ErrorCode(1_009_006_005, "导入数据格式错误");

    // ========== 考题分类管理模块 1-009-007-000 ==========
    ErrorCode QUESTION_CATEGORY_NOT_EXISTS = new ErrorCode(1_009_007_000, "分类不存在");
    ErrorCode QUESTION_CATEGORY_DELETED = new ErrorCode(1_009_007_001, "分类已被删除");
    ErrorCode QUESTION_CATEGORY_HAS_QUESTIONS = new ErrorCode(1_009_007_002, "分类下存在考题，无法删除");
    ErrorCode QUESTION_CATEGORY_CODE_DUPLICATE = new ErrorCode(1_009_007_003, "分类代码重复");
    ErrorCode QUESTION_CATEGORY_LEVEL_INVALID = new ErrorCode(1_009_007_004, "分类层级参数无效，只支持1、2、3");
    ErrorCode QUESTION_CATEGORY_HAS_CHILDREN = new ErrorCode(1_009_007_005, "分类下存在子分类，无法删除");

    // ========== 证书模板管理模块 1-001-008-000 ==========
    ErrorCode CERTIFICATE_TEMPLATE_NOT_EXISTS = new ErrorCode(1_001_008_000, "证书模板不存在");
    ErrorCode CERTIFICATE_TEMPLATE_NAME_DUPLICATE = new ErrorCode(1_001_008_001, "证书模板名称已存在");
    ErrorCode CERTIFICATE_TEMPLATE_STATUS_INVALID = new ErrorCode(1_001_008_002, "证书模板状态无效");
    ErrorCode CERTIFICATE_TEMPLATE_IN_USE = new ErrorCode(1_001_008_003, "证书模板正在使用中，无法删除");
    ErrorCode CERTIFICATE_TEMPLATE_FIELD_INVALID = new ErrorCode(1_001_008_004, "证书模板字段配置无效");
    ErrorCode CERTIFICATE_TEMPLATE_FIELD_POSITION_INVALID = new ErrorCode(1_001_008_005, "证书模板字段位置超出范围");
    ErrorCode CERTIFICATE_TEMPLATE_FIELD_COUNT_EXCEED = new ErrorCode(1_001_008_006, "证书模板字段数量超出限制");

    // ========== 场地管理模块 1-001-009-000 ==========
    ErrorCode SITE_MANAGEMENT_NOT_EXISTS = new ErrorCode(1_001_009_000, "场地不存在");
    ErrorCode SITE_MANAGEMENT_NAME_DUPLICATE = new ErrorCode(1_001_009_001, "场地名称已存在");
    ErrorCode SITE_MANAGEMENT_NOT_BOOKABLE = new ErrorCode(1_001_009_002, "场地状态不允许预约");

    // ========== 场地预约模块 1-001-010-000 ==========
    ErrorCode SITE_APPOINTMENT_NOT_EXISTS = new ErrorCode(1_001_010_000, "预约不存在");
    ErrorCode SITE_APPOINTMENT_TIME_CONFLICT = new ErrorCode(1_001_010_001, "时间段冲突");
    ErrorCode SITE_APPOINTMENT_NOT_CANCELLABLE = new ErrorCode(1_001_010_002, "预约状态不允许取消");
    ErrorCode SITE_APPOINTMENT_PEOPLE_COUNT_EXCEED = new ErrorCode(1_001_010_003, "预约人数超过场地容量");

}